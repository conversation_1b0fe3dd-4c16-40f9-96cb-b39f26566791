"""Views for venue search and detail pages."""

# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Max, Min, Q, Prefetch, Avg, Count
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse

# --- Local App Imports ---
from ..forms import FlaggedVenueForm, VenueFilterForm, VenueSearchForm
from ..models import Category, FlaggedVenue, Service, USCity, Venue


def venue_search(request):
    """Search and filter venues."""
    venues = Venue.objects.filter(
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE,
    ).exclude(
        approval_status=Venue.DRAFT
    ).select_related('service_provider').prefetch_related(
        'categories',
        Prefetch('services', queryset=Service.objects.filter(is_active=True).order_by('service_title')),
        'images'
    )

    venues = venues.annotate(
        min_service_price=Min('services__price_min'),
        max_service_price=Max('services__price_max'),
        avg_rating=Avg('reviews__rating', filter=Q(reviews__is_approved=True)),
        review_count=Count('reviews', filter=Q(reviews__is_approved=True)),
    )

    search_form = VenueSearchForm(request.GET or None)
    filter_form = VenueFilterForm(request.GET or None)

    applied_filters = {}

    if search_form.is_valid():
        query = search_form.cleaned_data.get('query')
        location = search_form.cleaned_data.get('location')
        category = search_form.cleaned_data.get('category')

        if query:
            venues = venues.filter(
                Q(venue_name__icontains=query)
                | Q(short_description__icontains=query)
                | Q(tags__icontains=query)
                | Q(services__service_title__icontains=query)
                | Q(services__short_description__icontains=query)
                | Q(categories__category_name__icontains=query)
                | Q(service_provider__legal_name__icontains=query)
                | Q(service_provider__display_name__icontains=query)
            ).distinct()
            applied_filters['query'] = query

        if location:
            city_matches = USCity.objects.filter(
                Q(city__icontains=location)
                | Q(state_name__icontains=location)
                | Q(county_name__icontains=location)
                | Q(state_id__iexact=location)
            )
            if city_matches.exists():
                venues = venues.filter(
                    Q(us_city__in=city_matches)
                    | Q(city__icontains=location)
                    | Q(state__icontains=location)
                    | Q(county__icontains=location)
                ).distinct()
            else:
                venues = venues.filter(
                    Q(city__icontains=location)
                    | Q(state__icontains=location)
                    | Q(county__icontains=location)
                )
            applied_filters['location'] = location

        if category:
            venues = venues.filter(categories=category)
            applied_filters['category'] = category.category_name

    if filter_form.is_valid():
        sort_by = filter_form.cleaned_data.get('sort_by')
        venue_type = filter_form.cleaned_data.get('venue_type')
        has_discount = filter_form.cleaned_data.get('has_discount')

        state = filter_form.cleaned_data.get('state')
        county = filter_form.cleaned_data.get('county')
        city = filter_form.cleaned_data.get('city')
        categories = filter_form.cleaned_data.get('categories')

        if venue_type:
            venues = venues.filter(venue_type=venue_type)
            applied_filters['venue_type'] = dict(filter_form.VENUE_TYPE_CHOICES).get(venue_type)

        if has_discount:
            venues = venues.filter(has_discount=True)
            applied_filters['has_discount'] = 'Has Discounts'



        if state:
            venues = venues.filter(Q(state__iexact=state) | Q(us_city__state_name__iexact=state))
            applied_filters['state'] = state

        if county:
            venues = venues.filter(Q(county__iexact=county) | Q(us_city__county_name__iexact=county))
            applied_filters['county'] = county

        if city:
            venues = venues.filter(Q(city__iexact=city) | Q(us_city__city__iexact=city))
            applied_filters['city'] = city

        if categories:
            venues = venues.filter(categories__in=categories).distinct()
            applied_filters['categories'] = [cat.name for cat in categories]

        if sort_by:
            if sort_by == 'rating_high':
                venues = venues.order_by('-created_at')
            elif sort_by == 'rating_low':
                venues = venues.order_by('created_at')
            elif sort_by == 'price_high':
                venues = venues.order_by('-min_service_price')
            elif sort_by == 'price_low':
                venues = venues.order_by('min_service_price')
            elif sort_by == 'newest':
                venues = venues.order_by('-created_at')
            elif sort_by == 'discount':
                venues = venues.order_by('-min_service_price')
            elif sort_by == 'name':
                venues = venues.order_by('venue_name')
        else:
            venues = venues.order_by('-created_at', 'venue_name')

    venues = venues.distinct()
    total_venues = venues.count()

    paginator = Paginator(venues, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    categories = Category.objects.filter(is_active=True).order_by('category_name')
    states = USCity.objects.values_list('state_name', flat=True).distinct().order_by('state_name')

    price_range = Service.objects.aggregate(
        min_price=Min('price_min'),
        max_price=Max('price_max'),
    )

    context = {
        'page_obj': page_obj,
        'venues': page_obj.object_list,
        'search_form': search_form,
        'filter_form': filter_form,
        'categories': categories,
        'states': states,
        'applied_filters': applied_filters,
        'total_venues': total_venues,
        'price_range': price_range,
        'is_search_results': bool(request.GET),
    }

    return render(request, 'venues_app/venue_search.html', context)


def location_autocomplete(request):
    """AJAX endpoint for location autocomplete suggestions."""
    query = request.GET.get('q', '').strip()
    if len(query) < 2:
        return JsonResponse({'suggestions': []})

    cache_key = f"location_autocomplete_{query.lower()}"
    suggestions = cache.get(cache_key)
    if suggestions is None:
        suggestions = []
        cities = USCity.objects.filter(
            Q(city__icontains=query)
            | Q(state_name__icontains=query)
            | Q(county_name__icontains=query)
            | Q(state_id__iexact=query)
        ).distinct()[:10]
        for city in cities:
            suggestions.append({
                'label': f"{city.city}, {city.state_id}",
                'value': f"{city.city}, {city.state_id}",
                'type': 'city',
                'state': city.state_name,
                'county': city.county_name,
            })

        states = USCity.objects.filter(state_name__icontains=query).values('state_name', 'state_id').distinct()[:5]
        for state in states:
            if not any(s['value'] == state['state_name'] for s in suggestions):
                suggestions.append({'label': state['state_name'], 'value': state['state_name'], 'type': 'state'})
        cache.set(cache_key, suggestions, 3600)

    return JsonResponse({'suggestions': suggestions})


def category_venues(request, category_slug):
    """Redirect to venue search filtered by category."""
    try:
        category = Category.objects.get(slug=category_slug, is_active=True)
        return redirect(f"{reverse('venues_app:venue_search')}?category={category.id}")
    except Category.DoesNotExist:
        messages.error(request, 'Category not found.')
        return redirect('venues_app:venue_search')


def get_location_data(request):
    """AJAX endpoint for hierarchical location data."""
    location_type = request.GET.get('type')
    state = request.GET.get('state')
    county = request.GET.get('county')

    data = {'options': []}

    if location_type == 'counties' and state:
        # Convert state abbreviation to state name for USCity lookup
        from ..models import Venue
        state_name = None
        for abbrev, name in Venue.STATE_CHOICES:
            if abbrev == state:
                state_name = name
                break

        if state_name:
            counties = USCity.objects.filter(state_name__iexact=state_name).values_list('county_name', flat=True).distinct().order_by('county_name')
            data['options'] = [{'value': county, 'label': county} for county in counties]
    elif location_type == 'cities' and state and county:
        # Convert state abbreviation to state name for USCity lookup
        from ..models import Venue
        state_name = None
        for abbrev, name in Venue.STATE_CHOICES:
            if abbrev == state:
                state_name = name
                break

        if state_name:
            cities = USCity.objects.filter(state_name__iexact=state_name, county_name__iexact=county).values_list('city', flat=True).distinct().order_by('city')
            data['options'] = [{'value': city, 'label': city} for city in cities]
    elif location_type == 'cities' and state:
        # Convert state abbreviation to state name for USCity lookup
        from ..models import Venue
        state_name = None
        for abbrev, name in Venue.STATE_CHOICES:
            if abbrev == state:
                state_name = name
                break

        if state_name:
            cities = USCity.objects.filter(state_name__iexact=state_name).values_list('city', flat=True).distinct().order_by('city')
            data['options'] = [{'value': city, 'label': city} for city in cities]

    return JsonResponse(data)


def venue_detail(request, venue_slug):
    """Display detailed information about a specific venue."""
    venue = (
        Venue.objects.select_related('service_provider', 'service_provider__user')
        .prefetch_related('services', 'faqs', 'images', 'categories', 'service_provider__team')
        .filter(slug=venue_slug)
        .first()
    )
    if not venue or venue.approval_status != Venue.APPROVED or venue.visibility != Venue.ACTIVE or venue.approval_status == Venue.DRAFT:
        return render(request, 'venues_app/venue_not_found.html', status=404)

    services = venue.services.filter(is_active=True).order_by('service_title')
    faqs = venue.faqs.filter(is_active=True).order_by('order')
    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    primary_image = images.filter(is_primary=True).first()
    gallery_images = images.filter(is_primary=False)
    
    # Get active team members for the venue's service provider
    team_members = venue.service_provider.team.filter(is_active=True).order_by('name')

    user_has_flagged = False
    if request.user.is_authenticated:
        user_has_flagged = FlaggedVenue.objects.filter(venue=venue, flagged_by=request.user).exists()

    price_range = None
    if services.exists():
        min_price = min(service.price_min for service in services)
        max_price = max(service.price_max or service.price_min for service in services)
        price_range = f"${min_price}" if min_price == max_price else f"${min_price} - ${max_price}"

    context = {
        'venue': venue,
        'services': services,
        'faqs': faqs,
        'images': images,
        'primary_image': primary_image,
        'gallery_images': gallery_images,
        'team_members': team_members,
        'user_has_flagged': user_has_flagged,
        'price_range': price_range,
        'can_flag': request.user.is_authenticated and not user_has_flagged,
    }
    return render(request, 'venues_app/venue_detail.html', context)


def service_detail(request, venue_slug, service_slug):
    """Display detailed information about a specific service."""
    venue = get_object_or_404(
        Venue.objects.select_related('service_provider', 'service_provider__user')
        .prefetch_related('categories'),
        slug=venue_slug,
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE
    )

    service = get_object_or_404(
        Service,
        slug=service_slug,
        venue=venue,
        is_active=True
    )

    # Get opening hours if they exist
    opening_hours = []
    try:
        from venues_app.models import OperatingHours
        opening_hours = OperatingHours.objects.filter(venue=venue).order_by('day')
    except ImportError:
        # OperatingHours model doesn't exist yet
        pass

    context = {
        'venue': venue,
        'service': service,
        'opening_hours': opening_hours,
    }
    return render(request, 'venues_app/service_detail.html', context)


@login_required
def flag_venue(request, venue_slug):
    """Allow customers to flag inappropriate venues."""
    venue = get_object_or_404(
        Venue.objects.select_related('service_provider'),
        slug=venue_slug,
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE,
    )

    rate_key = f"venue_flag_rate_{request.user.id}"
    attempts = cache.get(rate_key, 0)
    if attempts >= 3:
        messages.error(request, 'You have reached the flagging limit. Please try again later.')
        return redirect('venues_app:venue_detail', venue_slug=venue_slug)

    existing_flag = FlaggedVenue.objects.filter(venue=venue, flagged_by=request.user).first()
    if existing_flag:
        messages.info(request, 'You have already flagged this venue. Our team will review it.')
        return redirect('venues_app:venue_detail', venue_slug=venue_slug)

    if request.method == 'POST':
        form = FlaggedVenueForm(request.POST, venue=venue, user=request.user)
        if form.is_valid():
            form.save()
            cache.set(rate_key, attempts + 1, 3600)
            messages.success(request, 'Thank you for your report. Our team will review this venue and take appropriate action.')
            return redirect('venues_app:venue_detail', venue_slug=venue_slug)
        messages.error(request, 'Please correct the errors below.')
    else:
        form = FlaggedVenueForm(venue=venue, user=request.user)

    return render(request, 'venues_app/flag_venue.html', {'venue': venue, 'form': form})

