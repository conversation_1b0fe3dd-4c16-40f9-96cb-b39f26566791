{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Your Cart - CozyWish{% endblock %}

{% block booking_extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Cart Page */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Override base styles for cart page */
    .booking-wrapper {
        background: var(--cw-accent-light);
        font-family: var(--cw-font-primary);
    }

    /* Page Header */
    .cart-header {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
    }

    .cart-header h1 {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 2.5rem;
    }

    .cart-header .subtitle {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin: 0;
    }

    /* Cart Cards */
    .cart-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .cart-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .cart-card-header {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .cart-card-header h4 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        font-size: 1.5rem;
    }

    .cart-card-body {
        padding: 1.5rem;
    }

    /* Cart Items */
    .cart-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .cart-item:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-sm);
    }

    .cart-item h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .cart-item .service-description {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    .cart-item .service-details {
        margin-bottom: 1rem;
    }

    .cart-item .service-details p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .cart-item .service-details strong {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .cart-item .price-section {
        text-align: right;
    }

    .cart-item .price-section p {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .cart-item .total-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        color: white;
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-primary);
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-remove {
        background: white;
        border: 2px solid #dc3545;
        color: #dc3545;
        font-family: var(--cw-font-primary);
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .btn-remove:hover {
        background: #dc3545;
        color: white;
        transform: translateY(-1px);
    }

    /* Cart Summary */
    .cart-summary {
        background: var(--cw-gradient-card);
        border: 1px solid var(--cw-brand-primary);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 1.5rem;
    }

    .cart-summary h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        color: var(--cw-neutral-700);
        font-weight: 500;
    }

    .summary-total {
        border-top: 2px solid var(--cw-brand-primary);
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .summary-total .summary-row {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
    }

    /* Cart Expiry */
    .cart-expiry {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1rem;
        text-align: center;
    }

    .cart-expiry .expiry-text {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        margin: 0;
        font-weight: 500;
    }

    .cart-expiry .fas {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
    }

    /* Empty Cart */
    .empty-cart {
        text-align: center;
        padding: 4rem 2rem;
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .empty-cart .cart-icon {
        font-size: 4rem;
        color: var(--cw-brand-accent);
        margin-bottom: 1.5rem;
    }

    .empty-cart h5 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .empty-cart p {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    /* Checkout Section */
    .checkout-section {
        text-align: right;
        padding-top: 1.5rem;
        border-top: 2px solid var(--cw-brand-accent);
        margin-top: 1.5rem;
    }

    .checkout-total {
        font-family: var(--cw-font-heading);
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .cart-header h1 {
            font-size: 2rem;
        }

        .cart-item .price-section {
            text-align: left;
            margin-top: 1rem;
        }

        .checkout-section {
            text-align: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container">
    <!-- Page Header -->
    <div class="cart-header">
        <h1>Your Cart</h1>
        {% if cart.items.count > 0 %}
        <p class="subtitle">{{ cart.items.count }} item{{ cart.items.count|pluralize }} ready for booking</p>
        {% else %}
        <p class="subtitle">Start building your perfect spa experience</p>
        {% endif %}
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="cart-card">
                <div class="cart-card-header">
                    <h4>
                        <i class="fas fa-shopping-cart me-2"></i>
                        Cart Items
                    </h4>
                </div>
                <div class="cart-card-body">
                    {% if cart.items.count > 0 %}
                        {% for item in cart.items.all %}
                        <div class="cart-item">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>{{ item.service.service_title }}</h6>
                                    <p class="service-description">{{ item.service.short_description }}</p>
                                    <div class="service-details">
                                        <p><strong>Venue:</strong> {{ item.service.venue.venue_name }}</p>
                                        <p><strong>Date:</strong> {{ item.selected_date|date:"F d, Y" }}</p>
                                        <p><strong>Time:</strong> {{ item.selected_time_slot }}</p>
                                    </div>
                                </div>
                                <div class="col-md-4 price-section">
                                    <p><strong>Quantity:</strong> {{ item.quantity }}</p>
                                    <p><strong>Price:</strong> ${{ item.price_per_item }} each</p>
                                    <p class="total-price">Total: ${{ item.total_price }}</p>
                                    <form method="post" action="{% url 'booking_cart_app:remove_from_cart' item.id %}" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn-remove"
                                                onclick="return confirm('Remove this item from cart?')">
                                            <i class="fas fa-trash me-1"></i>
                                            Remove
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}

                        <div class="checkout-section">
                            <div class="checkout-total">Total: ${{ cart.total_price }}</div>
                            <a href="{% url 'booking_cart_app:checkout' %}" class="btn-cw-primary">
                                <i class="fas fa-credit-card me-2"></i>
                                Proceed to Checkout
                            </a>
                        </div>
                    {% else %}
                        <div class="empty-cart">
                            <div class="cart-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h5>Your cart is empty</h5>
                            <p>Discover amazing spa and wellness services to create your perfect relaxation experience.</p>
                            <a href="/" class="btn-cw-primary">
                                <i class="fas fa-search me-2"></i>
                                Browse Services
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {% if cart.items.count > 0 %}
            <div class="cart-summary">
                <h6>
                    <i class="fas fa-receipt me-2"></i>
                    Cart Summary
                </h6>
                <div class="summary-row">
                    <span>Items:</span>
                    <span>{{ cart.items.count }}</span>
                </div>
                <div class="summary-row">
                    <span>Subtotal:</span>
                    <span>${{ cart.total_price }}</span>
                </div>
                <div class="summary-total">
                    <div class="summary-row">
                        <span>Total:</span>
                        <span>${{ cart.total_price }}</span>
                    </div>
                </div>
            </div>

            <div class="cart-expiry">
                <p class="expiry-text">
                    <i class="fas fa-clock"></i>
                    Cart expires in {{ cart.expires_at|timeuntil }}
                </p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
