{% extends 'base.html' %}
{% load i18n %}
{% load review_tags %}

{% block title %}{% trans "Search Venues" %} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">

<style>
    /* CozyWish Design System - Professional Venue Search */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-300: #d4d4d4;
        --cw-neutral-400: #a3a3a3;
        --cw-neutral-500: #737373;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-900: #171717;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        background-color: #f8f9fa;
    }

    /* Sidebar Styles */
    .sidebar {
        max-height: 100vh;
        overflow-y: auto;
    }

    /* Filter Sidebar */
    .filter-sidebar {
        background: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .filter-title {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
        margin-bottom: 1.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        text-align: center;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .filter-section {
        margin-bottom: 1.5rem;
        background: rgba(255, 255, 255, 0.7);
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid var(--cw-neutral-200);
    }

    .filter-section:last-child {
        margin-bottom: 0;
    }

    .filter-section h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 0.875rem;
        margin-bottom: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    /* Form Controls in Sidebar */
    .filter-sidebar .form-control,
    .filter-sidebar .form-select {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        background: white;
    }

    .filter-sidebar .form-control:focus,
    .filter-sidebar .form-select:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    /* Radio and Checkbox Styling */
    .form-check {
        margin-bottom: 0.75rem;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .form-check:hover {
        background: var(--cw-brand-accent);
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-label {
        font-weight: 500;
        color: var(--cw-neutral-700);
        cursor: pointer;
    }

    /* Category List Styling */
    .category-list {
        max-height: 200px;
        overflow-y: auto;
        padding: 0.5rem 0;
    }

    .category-list::-webkit-scrollbar {
        width: 4px;
    }

    .category-list::-webkit-scrollbar-track {
        background: var(--cw-neutral-100);
        border-radius: 2px;
    }

    .category-list::-webkit-scrollbar-thumb {
        background: var(--cw-brand-primary);
        border-radius: 2px;
    }

    /* Form Controls */
    .form-control-sm {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    /* Venue Cards */
    .card-venue {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        box-shadow: var(--cw-shadow-sm);
        background: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        transition: all 0.3s ease;
        cursor: pointer;
        overflow: hidden;
    }

    .card-venue:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.15);
        transform: translateY(-2px);
        background: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    }

    .card-venue .card-body {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 0 0 0.75rem 0.75rem;
    }

    .card-venue ul li + li {
        border-top: 1px solid var(--cw-accent-dark);
    }

    /* Price Styling */
    .price-old {
        text-decoration: line-through;
        color: var(--cw-neutral-500);
    }

    .price-new {
        color: #c82333;
        font-weight: 600;
    }

    /* Service Items */
    .service-item {
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--cw-accent-dark);
        background: rgba(250, 225, 215, 0.3);
        margin: 0.25rem 0;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        transition: all 0.2s ease;
    }

    .service-item:hover {
        background: var(--cw-brand-accent);
        border-color: var(--cw-brand-primary);
        transform: translateX(5px);
    }

    .service-item:last-child {
        border-bottom: 1px solid var(--cw-accent-dark);
    }

    .service-name {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
        font-family: var(--cw-font-heading);
    }

    .service-meta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }

    .service-price {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
    }

    .service-duration {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
    }

    /* Buttons */
    .btn-cw-primary {
        background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        border: 2px solid var(--cw-brand-primary);
        color: white;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-family: var(--cw-font-heading);
        box-shadow: 0 2px 8px rgba(47, 22, 15, 0.2);
    }

    .btn-cw-primary:hover {
        background: linear-gradient(135deg, var(--cw-brand-light) 0%, var(--cw-brand-primary) 100%);
        border-color: var(--cw-brand-light);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
    }

    .btn-cw-secondary {
        background: linear-gradient(135deg, white 0%, var(--cw-accent-light) 100%);
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-family: var(--cw-font-heading);
        box-shadow: 0 2px 8px rgba(47, 22, 15, 0.1);
    }

    .btn-cw-secondary:hover {
        background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
    }

    /* Service Action Buttons */
    .btn-outline-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        background: white;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .btn-outline-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: scale(1.05);
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: 2px solid #28a745;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    /* View More Services Button */
    .btn-view-more {
        background: linear-gradient(135deg, var(--cw-accent-light) 0%, var(--cw-brand-accent) 100%);
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-family: var(--cw-font-heading);
        font-size: 0.875rem;
        box-shadow: 0 2px 8px rgba(47, 22, 15, 0.1);
        margin-top: 0.5rem;
        width: 100%;
        text-align: center;
    }

    .btn-view-more:hover {
        background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        text-decoration: none;
    }

    .btn-view-more i {
        margin-right: 0.5rem;
    }

    /* Badges */
    .badge-promotion {
        background: #ffc107;
        color: #212529;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
    }

    /* Rating Stars */
    .rating-stars {
        color: #ffc107;
    }

    /* Professional Pagination */
    .pagination-wrapper {
        background: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .pagination {
        margin-bottom: 0;
        justify-content: center;
        gap: 0.5rem;
    }

    .pagination .page-item .page-link {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        background: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.3s ease;
        margin: 0;
        min-width: 45px;
        text-align: center;
    }

    .pagination .page-item .page-link:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
    }

    .pagination .page-item.active .page-link {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
    }

    .pagination .page-item.disabled .page-link {
        border-color: var(--cw-neutral-300);
        color: var(--cw-neutral-400);
        background: var(--cw-neutral-100);
    }

    /* Card Title Styling */
    .card-venue .card-title {
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
    }

    /* Badge Styling */
    .badge-promotion {
        background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
        color: white;
        font-weight: 700;
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    }

    /* Responsive */
    @media (max-width: 1024px) {
        .container-fluid {
            padding: 1rem;
        }

        .pagination-wrapper {
            padding: 1rem;
        }

        .pagination .page-item .page-link {
            padding: 0.5rem 0.75rem;
            min-width: 40px;
        }
    }

    @media (max-width: 768px) {
        .row.g-4 {
            flex-direction: column;
        }

        .col-lg-3 {
            order: 2;
        }

        .col-lg-9 {
            order: 1;
        }

        .card-venue .card-body {
            padding: 1rem;
        }

        .service-item {
            padding: 0.5rem 0.75rem;
        }

        .pagination .page-item .page-link {
            padding: 0.5rem;
            min-width: 35px;
            font-size: 0.875rem;
        }
    }

</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row g-4">
        <!-- FILTER SIDEBAR -->
        <aside class="col-lg-3">
            <div class="filter-sidebar">
                <h6 class="filter-title">Search & Filter</h6>

                <form method="get" action="{% url 'venues_app:venue_search' %}">
                    <!-- Search Query -->
                    <div class="filter-section">
                        <h6><i class="bi bi-search me-2"></i>Search</h6>
                        <input type="text" name="query" class="form-control mb-2"
                               value="{{ search_form.query.value|default:'' }}"
                               placeholder="{% trans 'Search venues, services...' %}">
                    </div>

                    <!-- Location -->
                    <div class="filter-section">
                        <h6><i class="bi bi-geo-alt me-2"></i>Location</h6>
                        <input type="text" name="location" class="form-control mb-2"
                               value="{{ search_form.location.value|default:'' }}"
                               placeholder="{% trans 'City, State' %}">
                    </div>

                    <!-- Promotions Filter -->
                    <div class="filter-section">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="promotionsSwitch" name="promotions_only"
                                   {% if request.GET.promotions_only %}checked{% endif %}>
                            <label class="form-check-label fw-bold" for="promotionsSwitch">
                                <i class="bi bi-percent me-2"></i>Only show promotions
                            </label>
                        </div>
                    </div>

                    <!-- Sort Options -->
                    <div class="filter-section">
                        <h6><i class="bi bi-sort-down me-2"></i>Sort Options</h6>
                        {% for choice in filter_form.sort_by.field.choices %}
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="sort_by" id="sort{{ forloop.counter }}"
                                   value="{{ choice.0 }}" {% if filter_form.sort_by.value == choice.0 %}checked{% endif %}>
                            <label class="form-check-label" for="sort{{ forloop.counter }}">{{ choice.1 }}</label>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Categories -->
                    {% if search_form.category.field.choices %}
                    <div class="filter-section">
                        <h6><i class="bi bi-tags me-2"></i>Categories</h6>
                        <div class="category-list">
                            {% for choice in search_form.category.field.choices %}
                                {% if choice.0 %}
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="category" value="{{ choice.0 }}"
                                           id="cat{{ forloop.counter }}" {% if search_form.category.value == choice.0 %}checked{% endif %}>
                                    <label class="form-check-label" for="cat{{ forloop.counter }}">{{ choice.1 }}</label>
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Action Buttons -->
                    <div class="filter-section">
                        <button type="submit" class="btn btn-cw-primary w-100 mb-2">
                            <i class="bi bi-search me-2"></i>Apply Filters
                        </button>
                        <a href="{% url 'venues_app:venue_search' %}" class="btn btn-cw-secondary w-100">
                            <i class="bi bi-arrow-clockwise me-2"></i>Reset All
                        </a>
                    </div>
                </form>
            </div>
        </aside>

        <!-- VENUE LIST -->
        <section class="col-lg-9">
            <!-- Results Header -->
            <div class="mb-3">
                <h5 class="fw-bold text-dark">
                    {% if is_search_results %}
                        {{ total_venues }} venue{{ total_venues|pluralize }} found
                    {% else %}
                        Showing all {{ total_venues }} venue{{ total_venues|pluralize }}
                    {% endif %}
                </h5>
                {% if is_search_results %}
                <p class="text-muted small mb-0">
                    {% if search_form.query.value %}
                        Search results for "{{ search_form.query.value }}"
                    {% endif %}
                    {% if search_form.location.value %}
                        in {{ search_form.location.value }}
                    {% endif %}
                </p>
                {% endif %}
            </div>

            <!-- Venue Results -->
            {% if page_obj %}
                {% for venue in page_obj %}
                <!-- Venue Card -->
                <div class="card card-venue mb-4 shadow-sm" data-venue-url="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                    <div class="row g-0">
                        <div class="col-md-4 position-relative">
                            <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/600x400?text=Venue+Image' }}"
                                 class="img-fluid rounded-start" alt="{{ venue.venue_name }}"
                                 style="height: 250px; object-fit: cover; border-radius: 0.75rem 0 0 0.75rem;" />
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-start"
                                 style="background: linear-gradient(45deg, rgba(47, 22, 15, 0.1) 0%, transparent 50%); border-radius: 0.75rem 0 0 0.75rem;"></div>
                        </div>
                        <div class="col-md-8">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <h5 class="card-title mb-0">{{ venue.venue_name }}</h5>
                                    {% if venue.has_promotions %}
                                    <span class="badge badge-promotion"><i class="bi bi-percent"></i> PROMOTIONS</span>
                                    {% endif %}
                                </div>
                                <p class="card-text small text-muted mb-1">
                                    <i class="bi bi-geo-alt"></i> {{ venue.city }}, {{ venue.state }}
                                </p>

                                <!-- Rating -->
                                <p class="mb-2">
                                    {% if venue.avg_rating %}
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= venue.avg_rating %}
                                                <i class="bi bi-star-fill rating-stars"></i>
                                            {% else %}
                                                <i class="bi bi-star rating-stars"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="small text-muted ms-1">({{ venue.review_count|default:"0" }} review{{ venue.review_count|default:"0"|pluralize }})</span>
                                    {% else %}
                                        <span class="badge bg-primary">New on CozyWish</span>
                                    {% endif %}
                                </p>

                                <!-- Services List -->
                                {% if venue.services.all %}
                                <ul class="list-unstyled mb-0">
                                    {% for service in venue.services.all|slice:":3" %}
                                    <li class="service-item d-flex align-items-center py-2">
                                        <div class="flex-grow-1">
                                            <strong class="service-name">{{ service.service_title }}</strong> &bull; {{ service.duration_display }}
                                            {% if service.short_description %}
                                            <div class="small text-muted">{{ service.short_description|truncatewords:8 }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="text-end">
                                            {% if service.discounted_price and service.discounted_price != service.price %}
                                                <span class="price-old">${{ service.price }}</span>
                                                <span class="price-new ms-1">${{ service.discounted_price }}</span>
                                            {% else %}
                                                <span class="service-price">${{ service.price }}</span>
                                            {% endif %}
                                            {% if user.is_authenticated and user.is_customer %}
                                            <button type="button" class="btn btn-sm btn-outline-secondary ms-2"
                                                    data-service-id="{{ service.id }}"
                                                    data-service-name="{{ service.service_title }}"
                                                    data-venue-name="{{ venue.venue_name }}"
                                                    onclick="event.stopPropagation(); showAddToCartModal({{ service.id }}, '{{ service.service_title }}', '{{ venue.venue_name }}');">
                                                <i class="bi bi-cart-plus"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </li>
                                    {% endfor %}
                                </ul>
                                {% if venue.services.all.count > 3 %}
                                <div class="text-center mt-3">
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-view-more">
                                        <i class="bi bi-plus-circle"></i>
                                        View {{ venue.services.all.count|add:"-3" }} more service{{ venue.services.all.count|add:"-3"|pluralize }}
                                    </a>
                                </div>
                                {% endif %}
                                {% else %}
                                <p class="text-muted">No services available</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="pagination-wrapper">
                <nav aria-label="Search results pagination">
                    <ul class="pagination">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" aria-label="Previous page" title="Previous">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" aria-label="Previous page disabled">
                                    <i class="bi bi-chevron-left"></i>
                                </span>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active" aria-current="page">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" title="Go to page {{ num }}">{{ num }}</a>
                                </li>
                            {% elif num == 1 or num == page_obj.paginator.num_pages %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" title="Go to page {{ num }}">{{ num }}</a>
                                </li>
                            {% elif num == page_obj.number|add:'-4' or num == page_obj.number|add:'4' %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" aria-label="Next page" title="Next">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" aria-label="Next page disabled">
                                    <i class="bi bi-chevron-right"></i>
                                </span>
                            </li>
                        {% endif %}
                    </ul>

                    <!-- Page Info -->
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            Showing page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            ({{ page_obj.paginator.count }} total venue{{ page_obj.paginator.count|pluralize }})
                        </small>
                    </div>
                </nav>
            </div>
            {% endif %}

            {% else %}
            <!-- No Results -->
            <div class="text-center py-5">
                <i class="bi bi-search display-1 text-muted mb-3"></i>
                <h3 class="text-dark">{% trans "No venues found" %}</h3>
                <p class="text-muted">{% trans "Try adjusting your search criteria or browse all venues." %}</p>
                <a href="{% url 'venues_app:venue_search' %}" class="btn btn-cw-primary">
                    <i class="bi bi-list me-2"></i>{% trans "Browse All Venues" %}
                </a>
            </div>
            {% endif %}
        </section>
    </div>
</div>

<!-- Add to Cart Modal -->
{% if user.is_authenticated and user.is_customer %}
<div class="modal fade" id="addToCartModal" tabindex="-1" aria-labelledby="addToCartModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addToCartModalLabel">
                    {% trans "Add Service to Cart" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="modalServiceInfo" class="mb-3 p-3 bg-light rounded">
                    <h6 id="modalServiceName" class="mb-1 fw-bold"></h6>
                    <p class="mb-0 text-muted small" id="modalVenueName"></p>
                </div>
                <form id="addToCartForm" method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="selected_date" class="form-label fw-bold">{% trans "Select Date" %}</label>
                        <input type="date" class="form-control" id="selected_date" name="selected_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="selected_time_slot" class="form-label fw-bold">{% trans "Select Time" %}</label>
                        <select class="form-select" id="selected_time_slot" name="selected_time_slot" required>
                            <option value="">{% trans "Choose a time slot..." %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="quantity" class="form-label fw-bold">{% trans "Quantity" %}</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" max="10" value="1" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-cw-primary" id="confirmAddToCart">{% trans "Add to Cart" %}</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make venue cards clickable
    const venueCards = document.querySelectorAll('.card-venue');
    venueCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on buttons
            if (e.target.closest('button') || e.target.closest('.btn')) {
                return;
            }

            const venueUrl = this.dataset.venueUrl;
            if (venueUrl) {
                window.location.href = venueUrl;
            }
        });
    });

    {% if user.is_authenticated and user.is_customer %}
    // Add to cart functionality
    let currentServiceId = null;
    const addToCartModal = new bootstrap.Modal(document.getElementById('addToCartModal'));

    // Set minimum date to tomorrow
    const dateInput = document.getElementById('selected_date');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.min = tomorrow.toISOString().split('T')[0];

    // Set maximum date to 30 days from now
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    dateInput.max = maxDate.toISOString().split('T')[0];

    // Show add to cart modal
    window.showAddToCartModal = function(serviceId, serviceName, venueName) {
        currentServiceId = serviceId;
        document.getElementById('modalServiceName').textContent = serviceName;
        document.getElementById('modalVenueName').textContent = venueName;

        // Reset form
        document.getElementById('addToCartForm').reset();
        document.getElementById('selected_time_slot').innerHTML = '<option value="">Choose a time slot...</option>';

        addToCartModal.show();
    };

    // Load time slots when date changes
    dateInput.addEventListener('change', function() {
        const selectedDate = this.value;
        const timeSlotSelect = document.getElementById('selected_time_slot');

        if (selectedDate && currentServiceId) {
            // Clear existing options
            timeSlotSelect.innerHTML = '<option value="">Loading...</option>';

            // Fetch available time slots
            fetch(`/bookings/ajax/slots/${currentServiceId}/?date=${selectedDate}`)
                .then(response => response.json())
                .then(data => {
                    timeSlotSelect.innerHTML = '<option value="">Choose a time slot...</option>';

                    if (data.slots && data.slots.length > 0) {
                        data.slots.forEach(slot => {
                            const option = document.createElement('option');
                            option.value = slot.time;
                            option.textContent = `${slot.display} (${slot.available_spots} available)`;
                            timeSlotSelect.appendChild(option);
                        });
                    } else {
                        timeSlotSelect.innerHTML = '<option value="">No available time slots</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading time slots:', error);
                    timeSlotSelect.innerHTML = '<option value="">Error loading time slots</option>';
                });
        }
    });

    // Handle add to cart confirmation
    document.getElementById('confirmAddToCart').addEventListener('click', function() {
        const form = document.getElementById('addToCartForm');
        const formData = new FormData(form);

        if (currentServiceId && form.checkValidity()) {
            // Submit form to add to cart
            fetch(`/bookings/cart/add/${currentServiceId}/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => {
                if (response.ok) {
                    addToCartModal.hide();
                    // Show success message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                    alertDiv.innerHTML = `
                        <strong>Success!</strong> Service added to cart.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(alertDiv);

                    // Auto-remove alert after 3 seconds
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.remove();
                        }
                    }, 3000);
                } else {
                    throw new Error('Failed to add to cart');
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                // Show error message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                alertDiv.innerHTML = `
                    <strong>Error!</strong> Failed to add service to cart. Please try again.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);

                // Auto-remove alert after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            });
        } else {
            // Show validation errors
            form.reportValidity();
        }
    });
    {% endif %}
});
</script>
{% endblock %}
